<?php
// 数据库连接调试工具
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>🔍 数据库连接调试工具</h1>";

echo "<h2>1. 基础环境检查</h2>";
echo "PHP版本: " . phpversion() . "<br>";
echo "当前时间: " . date('Y-m-d H:i:s') . "<br>";
echo "当前目录: " . __DIR__ . "<br>";

// 检查关键文件
echo "<h2>2. 关键文件检查</h2>";
$files = array(
    'config.php' => '数据库配置文件',
    'source/init.php' => '系统初始化文件',
    'source/include/mysql.php' => 'MySQL类文件'
);

foreach ($files as $file => $desc) {
    if (file_exists($file)) {
        echo "✅ {$desc}: {$file}<br>";
    } else {
        echo "❌ {$desc}: {$file} 不存在<br>";
    }
}

// 尝试加载配置文件
echo "<h2>3. 配置文件加载测试</h2>";
try {
    define('IN_IWEBDIR', true);
    require_once('config.php');
    echo "✅ 配置文件加载成功<br>";
    
    // 显示数据库配置
    echo "<h3>数据库配置信息:</h3>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>配置项</th><th>值</th></tr>";
    echo "<tr><td>DB_HOST</td><td>" . (defined('DB_HOST') ? DB_HOST : '未定义') . "</td></tr>";
    echo "<tr><td>DB_PORT</td><td>" . (defined('DB_PORT') ? DB_PORT : '未定义') . "</td></tr>";
    echo "<tr><td>DB_USER</td><td>" . (defined('DB_USER') ? DB_USER : '未定义') . "</td></tr>";
    echo "<tr><td>DB_PASS</td><td>" . (defined('DB_PASS') ? (DB_PASS ? '已设置' : '为空') : '未定义') . "</td></tr>";
    echo "<tr><td>DB_NAME</td><td>" . (defined('DB_NAME') ? DB_NAME : '未定义') . "</td></tr>";
    echo "<tr><td>DB_CHARSET</td><td>" . (defined('DB_CHARSET') ? DB_CHARSET : '未定义') . "</td></tr>";
    echo "<tr><td>TABLE_PREFIX</td><td>" . (defined('TABLE_PREFIX') ? TABLE_PREFIX : '未定义') . "</td></tr>";
    echo "</table>";
    
} catch (Exception $e) {
    echo "❌ 配置文件加载异常: " . $e->getMessage() . "<br>";
    exit;
} catch (Error $e) {
    echo "❌ 配置文件加载错误: " . $e->getMessage() . "<br>";
    exit;
}

// 检查MySQL扩展
echo "<h2>4. MySQL扩展检查</h2>";
if (extension_loaded('mysqli')) {
    echo "✅ MySQLi 扩展已加载<br>";
} else {
    echo "❌ MySQLi 扩展未加载<br>";
}

if (extension_loaded('mysql')) {
    echo "✅ MySQL 扩展已加载<br>";
} else {
    echo "⚠️ MySQL 扩展未加载（PHP 7.0+ 已移除）<br>";
}

if (extension_loaded('pdo_mysql')) {
    echo "✅ PDO MySQL 扩展已加载<br>";
} else {
    echo "❌ PDO MySQL 扩展未加载<br>";
}

// 测试原生MySQL连接
echo "<h2>5. 原生MySQL连接测试</h2>";
if (extension_loaded('mysqli')) {
    try {
        $mysqli = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME, DB_PORT);
        
        if ($mysqli->connect_error) {
            echo "❌ MySQLi连接失败: " . $mysqli->connect_error . "<br>";
        } else {
            echo "✅ MySQLi连接成功<br>";
            echo "服务器信息: " . $mysqli->server_info . "<br>";
            echo "客户端信息: " . $mysqli->client_info . "<br>";
            
            // 测试查询
            $result = $mysqli->query("SELECT 1 as test");
            if ($result) {
                echo "✅ 查询测试成功<br>";
                $result->close();
            } else {
                echo "❌ 查询测试失败: " . $mysqli->error . "<br>";
            }
            
            $mysqli->close();
        }
    } catch (Exception $e) {
        echo "❌ MySQLi连接异常: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ 无法测试MySQLi连接，扩展未加载<br>";
}

// 尝试加载系统MySQL类
echo "<h2>6. 系统MySQL类测试</h2>";
try {
    // 确保常量定义
    if (!defined('ROOT_PATH')) {
        define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
    }
    if (!defined('APP_PATH')) {
        define('APP_PATH', ROOT_PATH.'source/');
    }
    
    if (file_exists(APP_PATH.'include/mysql.php')) {
        echo "✅ MySQL类文件存在<br>";
        require_once(APP_PATH.'include/mysql.php');
        echo "✅ MySQL类文件加载成功<br>";
        
        // 尝试创建数据库对象
        try {
            $DB = new MySQL(DB_HOST, DB_PORT, DB_USER, DB_PASS, DB_NAME, DB_CHARSET, TABLE_PREFIX, DB_PCONNECT);
            echo "✅ MySQL类实例化成功<br>";
            
            if (is_object($DB)) {
                echo "✅ 数据库对象类型正确<br>";
                
                // 测试查询
                try {
                    $test_query = $DB->query("SELECT 1 as test");
                    if ($test_query) {
                        echo "✅ 数据库查询测试成功<br>";
                    } else {
                        echo "❌ 数据库查询测试失败<br>";
                    }
                } catch (Exception $e) {
                    echo "❌ 数据库查询异常: " . $e->getMessage() . "<br>";
                }
            } else {
                echo "❌ 数据库对象类型错误<br>";
            }
            
        } catch (Exception $e) {
            echo "❌ MySQL类实例化异常: " . $e->getMessage() . "<br>";
        } catch (Error $e) {
            echo "❌ MySQL类实例化错误: " . $e->getMessage() . "<br>";
        }
        
    } else {
        echo "❌ MySQL类文件不存在: " . APP_PATH.'include/mysql.php' . "<br>";
    }
    
} catch (Exception $e) {
    echo "❌ 系统MySQL类测试异常: " . $e->getMessage() . "<br>";
} catch (Error $e) {
    echo "❌ 系统MySQL类测试错误: " . $e->getMessage() . "<br>";
}

// 尝试完整的系统初始化
echo "<h2>7. 完整系统初始化测试</h2>";
try {
    if (file_exists('source/init.php')) {
        echo "✅ 初始化文件存在<br>";
        
        // 重新开始，清理之前的定义
        echo "尝试加载完整系统...<br>";
        require_once('source/init.php');
        echo "✅ 系统初始化完成<br>";
        
        // 检查全局变量
        if (isset($GLOBALS['DB'])) {
            echo "✅ 全局数据库对象存在<br>";
        } else {
            echo "❌ 全局数据库对象不存在<br>";
        }
        
        if (isset($DB)) {
            echo "✅ 局部数据库对象存在<br>";
        } else {
            echo "❌ 局部数据库对象不存在<br>";
        }
        
    } else {
        echo "❌ 初始化文件不存在<br>";
    }
    
} catch (Exception $e) {
    echo "❌ 系统初始化异常: " . $e->getMessage() . "<br>";
    echo "异常文件: " . $e->getFile() . " 第 " . $e->getLine() . " 行<br>";
} catch (Error $e) {
    echo "❌ 系统初始化错误: " . $e->getMessage() . "<br>";
    echo "错误文件: " . $e->getFile() . " 第 " . $e->getLine() . " 行<br>";
}

echo "<h2>8. 诊断建议</h2>";
echo "<div style='background: #fff3cd; padding: 15px; border-left: 4px solid #ffc107;'>";
echo "<h3>如果数据库连接失败，请检查：</h3>";
echo "<ul>";
echo "<li><strong>数据库服务</strong> - 确认MySQL/MariaDB服务正在运行</li>";
echo "<li><strong>连接参数</strong> - 检查主机名、端口、用户名、密码是否正确</li>";
echo "<li><strong>数据库权限</strong> - 确认数据库用户有足够的权限</li>";
echo "<li><strong>防火墙设置</strong> - 确认数据库端口没有被防火墙阻止</li>";
echo "<li><strong>PHP扩展</strong> - 确认MySQLi扩展已正确安装和启用</li>";
echo "</ul>";
echo "</div>";

echo "<h2>9. 测试链接</h2>";
echo "<p>";
echo "<a href='final_quicksubmit_test.php' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>🔄 重新测试快速提交</a>";
echo "<a href='?mod=quicksubmit' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>📝 访问快速提交页面</a>";
echo "</p>";
?>
