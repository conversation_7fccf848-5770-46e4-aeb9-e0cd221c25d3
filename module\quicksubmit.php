<?php
if (!defined('IN_IWEBDIR')) exit('Access Denied');

$pagename = '快速提交网站';
$tempfile = 'quicksubmit.html';
$pageurl = '?mod=quicksubmit';

// 处理表单提交
if ($_POST['act'] == 'submit') {
	// 检查提交功能是否开启
	if (isset($options['is_enabled_submit']) && $options['is_enabled_submit'] != 'yes') {
		$reason = isset($options['submit_close_reason']) ? $options['submit_close_reason'] : '网站提交功能暂时关闭，请稍后再试。';
		echo "<script>alert('" . addslashes($reason) . "'); history.back();</script>";
		exit;
	}
	
	// 获取并清理表单数据
	$cate_id = intval($_POST['cate_id']);
	$web_name = trim($_POST['web_name']);
	$web_url = trim($_POST['web_url']);
	$web_tags = strtolower(addslashes(trim($_POST['web_tags'])));
	$web_intro = addslashes(trim($_POST['web_intro']));
	$web_owner = trim($_POST['web_owner']);
	$web_email = trim($_POST['web_email']);
	$check_code = strtolower(trim($_POST['check_code']));
	
	// 验证码检查
	if (empty($check_code) || $check_code != $_SESSION['code']) {
		echo "<script>alert('验证码错误！请重新输入验证码。'); history.back();</script>";
		exit;
	}
	
	// 数据验证
	if ($cate_id <= 0) {
		echo "<script>alert('请选择网站所属分类！'); history.back();</script>";
		exit;
	}
	
	if (empty($web_name)) {
		echo "<script>alert('请输入网站名称！'); history.back();</script>";
		exit;
	}
	
	// 检查网站名称长度（中文算2个字符）
	$name_length = 0;
	for ($i = 0; $i < mb_strlen($web_name, 'UTF-8'); $i++) {
		$char = mb_substr($web_name, $i, 1, 'UTF-8');
		if (ord($char) > 127) {
			$name_length += 2; // 中文字符算2个字符
		} else {
			$name_length += 1; // 英文字符算1个字符
		}
	}
	
	if ($name_length > 12) {
		echo "<script>alert('网站名称过长！最多12个字符（6个汉字）'); history.back();</script>";
		exit;
	}
	
	if (empty($web_url)) {
		echo "<script>alert('请输入网站域名！'); history.back();</script>";
		exit;
	} else {
		// 清理URL，移除协议前缀
		$web_url = preg_replace('/^https?:\/\//', '', $web_url);
		$web_url = preg_replace('/^www\./', '', $web_url);
		$web_url = rtrim($web_url, '/');
		
		// 域名格式检查
		if (!preg_match('/^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$/', $web_url)) {
			echo "<script>alert('请输入正确的网站域名！例如：example.com'); history.back();</script>";
			exit;
		}
		
		// 检查域名长度
		if (strlen($web_url) > 100) {
			echo "<script>alert('网站域名过长！'); history.back();</script>";
			exit;
		}
	}
	
	if (empty($web_intro)) {
		echo "<script>alert('请输入网站简介！'); history.back();</script>";
		exit;
	}
	
	// 检查简介长度
	if (mb_strlen($web_intro, 'UTF-8') < 10) {
		echo "<script>alert('网站简介至少需要10个字符！'); history.back();</script>";
		exit;
	}
	
	if (mb_strlen($web_intro, 'UTF-8') > 500) {
		echo "<script>alert('网站简介不能超过500个字符！'); history.back();</script>";
		exit;
	}
	
	if (empty($web_email)) {
		echo "<script>alert('请输入电子邮箱！'); history.back();</script>";
		exit;
	} else {
		if (!filter_var($web_email, FILTER_VALIDATE_EMAIL)) {
			echo "<script>alert('请输入正确的电子邮箱！'); history.back();</script>";
			exit;
		}
	}
	
	// 检查网站是否已存在
	$table = $DB->table('websites');
	$query = $DB->query("SELECT web_id, web_name, web_status, web_ctime FROM $table WHERE web_url='$web_url'");
	if ($DB->num_rows($query)) {
		$existing_web = $DB->fetch_array($query);
		$status_msg = '';
		
		switch($existing_web['web_status']) {
			case 1:
				$status_msg = '该网站已被拉黑，无法重复提交！';
				break;
			case 2:
				$status_msg = '该网站正在审核中，请勿重复提交！';
				break;
			case 3:
				$status_msg = '该网站已收录（收录时间：' . date('Y-m-d', $existing_web['web_ctime']) . '），请勿重复提交！';
				break;
			case 4:
				$status_msg = '该网站审核不通过，请修改后重新提交！';
				break;
			default:
				$status_msg = '该网站已存在，请勿重复提交！';
		}
		
		echo "<script>alert('$status_msg'); history.back();</script>";
		exit;
	}
	
	// 准备数据 - 参照会员提交逻辑
	$web_time = time();
	$web_ip = get_client_ip();

	// 处理TAG标签格式
	if (!empty($web_tags)) {
		$web_tags = str_replace('，', ',', $web_tags);
		$web_tags = str_replace(',,', ',', $web_tags);
		if (substr($web_tags, -1) == ',') {
			$web_tags = substr($web_tags, 0, strlen($web_tags) - 1);
		}
	}

	$web_data = array(
		'cate_id' => $cate_id,
		'user_id' => 0, // 非会员用户ID为0
		'web_name' => $web_name,
		'web_url' => $web_url,
		'web_tags' => $web_tags,
		'web_intro' => $web_intro,
		'web_status' => 2, // 待审核状态
		'web_ctime' => $web_time
	);
	
	// 插入数据
	$DB->insert($table, $web_data);
	$web_id = $DB->insert_id();
	
	// 插入统计数据 - 参照会员提交逻辑
	$web_ip_numeric = sprintf("%u", ip2long($web_ip));
	$stat_data = array(
		'web_id' => $web_id,
		'web_ip' => $web_ip_numeric,
		'web_grank' => 0,
		'web_brank' => 0,
		'web_srank' => 0,
		'web_arank' => 0,
		'web_utime' => $web_time
	);
	$DB->insert($DB->table('webdata'), $stat_data);
	
	// 更新分类统计
	$DB->query("UPDATE ".$DB->table('categories')." SET cate_postcount=cate_postcount+1 WHERE cate_id=$cate_id");
	
	// 更新缓存
	update_cache('archives');

	// 发送邮件通知管理员 - 简化版本但功能完整
	try {
		// 检查邮件配置
		if (!empty($options['smtp_host']) && !empty($options['smtp_port']) &&
			!empty($options['smtp_user']) && !empty($options['smtp_pass']) &&
			!empty($options['admin_email'])) {

			require_once(APP_PATH.'include/sendmail.php');

			// 获取分类信息
			$category_info = $DB->fetch_one("SELECT cate_name FROM ".$DB->table('categories')." WHERE cate_id=".$web_data['cate_id']);
			$category_name = $category_info ? $category_info['cate_name'] : '未知分类';

			// 构建邮件内容
			$subject = "[" . $options['site_name'] . "] 有新的网站快速提交需要审核";

			// 清理用户输入，避免邮件内容出现问题
			$safe_web_name = htmlspecialchars($web_data['web_name'], ENT_QUOTES, 'UTF-8');
			$safe_web_url = htmlspecialchars($web_data['web_url'], ENT_QUOTES, 'UTF-8');
			$safe_web_intro = htmlspecialchars(substr($web_data['web_intro'], 0, 200), ENT_QUOTES, 'UTF-8');
			$safe_web_tags = htmlspecialchars($web_data['web_tags'], ENT_QUOTES, 'UTF-8');

			$mailbody = "
			<h2>有新的网站快速提交需要审核</h2>
			<p><strong>网站名称：</strong>" . $safe_web_name . "</p>
			<p><strong>网站地址：</strong><a href=\"http://" . $safe_web_url . "\" target=\"_blank\">" . $safe_web_url . "</a></p>
			<p><strong>网站分类：</strong>" . $category_name . "</p>
			<p><strong>TAG标签：</strong>" . $safe_web_tags . "</p>
			<p><strong>网站简介：</strong>" . $safe_web_intro . "</p>
			<p><strong>提交时间：</strong>" . date('Y-m-d H:i:s', $web_data['web_ctime']) . "</p>
			<p><a href=\"" . $options['site_url'] . "system/\" target=\"_blank\">点击进入后台审核</a></p>
			<hr>
			<p style=\"font-size: 12px; color: #666;\">此邮件由 " . $options['site_name'] . " 系统自动发送，请勿直接回复。</p>
			";

			$result = sendmail($options['admin_email'], $subject, $mailbody);

			// 记录发送结果
			@error_log("快速提交邮件通知 - " . date('Y-m-d H:i:s') . " - 结果: " . ($result ? 'SUCCESS' : 'FAILED') . "\n", 3, "data/email_debug.log");
		}
	} catch (Exception $e) {
		// 记录错误但不影响用户体验
		@error_log("快速提交邮件通知发送异常: " . $e->getMessage() . "\n", 3, "data/email_error.log");
	}

	// 成功提交的消息 - 简化并确保JavaScript安全
	$success_msg = "网站提交成功！\n\n";
	$success_msg .= "网站名称：" . $web_name . "\n";
	$success_msg .= "网站域名：" . $web_url . "\n";
	$success_msg .= "提交时间：" . date('Y-m-d H:i:s') . "\n\n";
	$success_msg .= "我们会在1-3个工作日内审核您的网站，请耐心等待。\n";
	$success_msg .= "感谢您的提交！";

	// 清理消息内容，确保JavaScript安全
	$safe_msg = str_replace(array("\r", "\n"), array("", "\\n"), addslashes($success_msg));

	echo "<script>";
	echo "alert('" . $safe_msg . "');";
	echo "window.location.href = '?mod=index';";
	echo "</script>";
	exit;
}

// 获取分类选项
$category_option = get_category_option('webdir', 0);

// 设置模板变量
$smarty->assign('site_title', $pagename.' - '.$options['site_name']);
$smarty->assign('site_keywords', '快速提交网站,网站收录,免费收录');
$smarty->assign('site_description', '快速提交网站到分类目录，无需注册，直接提交。');
$smarty->assign('site_path', '当前位置：<a href="'.$options['site_url'].'">'.$options['site_name'].'</a> &raquo; '.$pagename);
$smarty->assign('pageurl', $pageurl);
$smarty->assign('category_option', $category_option);

// 从配置中获取提交设置
$cfg = array(
	'is_enabled_submit' => isset($options['is_enabled_submit']) ? $options['is_enabled_submit'] : 'yes',
	'submit_close_reason' => isset($options['submit_close_reason']) ? $options['submit_close_reason'] : '网站提交功能暂时关闭，请稍后再试。'
);
$smarty->assign('cfg', $cfg);

// 发送审核信息到微信/钉钉机器人
function send_msg($web_data, $options, $type='wechat_robot'){
    $action = '快速提交';
    $messageData = [
        "msgtype" => "text",
        "text" => [
            "content" => "有".$action."网站需要审核\n站点名称：".$web_data['web_name']."\n站点地址：".$web_data['web_url']."\n提交时间：".date('Y-m-d H:i:s',$web_data['web_ctime']),
        ]
    ];

    $webhook_url = '';
    $secret = '';

    if ($type == 'wechat_robot') {
        $webhook_url = $options['wechat_robot'];
    } elseif ($type == 'dingding_robot') {
        $webhook_url = $options['dingding_robot'];
        $secret = $options['dingding_secret'];

        // 钉钉机器人需要签名
        if (!empty($secret)) {
            $timestamp = round(microtime(true) * 1000);
            $string_to_sign = $timestamp . "\n" . $secret;
            $sign = base64_encode(hash_hmac('sha256', $string_to_sign, $secret, true));
            $webhook_url .= "&timestamp=" . $timestamp . "&sign=" . urlencode($sign);
        }
    }

    if (!empty($webhook_url)) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $webhook_url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($messageData));
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);

        $result = curl_exec($ch);
        curl_close($ch);

        return $result;
    }

    return false;
}

// 发送管理员邮件通知
function send_admin_email_notification($web_data, $options, $action_type) {
    global $smarty, $DB;

    // 检查必要的配置
    $required_configs = ['smtp_host', 'smtp_port', 'smtp_auth', 'smtp_user', 'smtp_pass', 'admin_email'];
    foreach ($required_configs as $config) {
        if (empty($options[$config])) {
            return false;
        }
    }

    // 包含邮件发送函数
    require_once(APP_PATH.'include/sendmail.php');

    // 获取分类信息
    $category_info = $DB->fetch_one("SELECT cate_name FROM ".$DB->table('categories')." WHERE cate_id=".$web_data['cate_id']);
    $category_name = $category_info ? $category_info['cate_name'] : '未知分类';

    // 设置模板变量
    $smarty->assign('site_name', $options['site_name']);
    $smarty->assign('site_url', $options['site_url']);
    $smarty->assign('action_type', $action_type);
    $smarty->assign('web_name', $web_data['web_name']);
    $smarty->assign('web_url', $web_data['web_url']);
    $smarty->assign('web_intro', $web_data['web_intro']);
    $smarty->assign('web_tags', $web_data['web_tags']);
    $smarty->assign('category_name', $category_name);
    $smarty->assign('user_email', '快速提交用户（非会员）');
    $smarty->assign('submit_time', date('Y年m月d日 H:i:s', $web_data['web_ctime']));

    // 邮件主题和内容
    $subject = '['.$options['site_name'].'] 有新的网站'.$action_type.'需要审核';

    // 构建邮件内容（使用内联HTML，避免模板文件问题）
    $mailbody = '<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>网站审核通知</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px 10px 0 0; text-align: center;">
        <h1 style="margin: 0; font-size: 24px;">🔔 网站审核通知</h1>
        <p style="margin: 10px 0 0 0; opacity: 0.9;">有新的网站' . $action_type . '需要您的审核</p>
    </div>

    <div style="background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; border: 1px solid #e9ecef;">
        <p style="font-size: 16px; margin-bottom: 20px;">
            <strong>尊敬的管理员：</strong><br>
            您好！有用户通过快速提交功能提交了新网站，请及时审核处理。
        </p>

        <table style="width: 100%; border-collapse: collapse; margin: 20px 0; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <tr style="background: #007bff; color: white;">
                <td colspan="2" style="padding: 15px; text-align: center; font-weight: bold; font-size: 18px;">
                    📋 网站详细信息
                </td>
            </tr>
            <tr style="border-bottom: 1px solid #dee2e6;">
                <td style="padding: 12px 15px; font-weight: bold; background: #f8f9fa; width: 120px;">操作类型</td>
                <td style="padding: 12px 15px;">' . $action_type . '</td>
            </tr>
            <tr style="border-bottom: 1px solid #dee2e6;">
                <td style="padding: 12px 15px; font-weight: bold; background: #f8f9fa;">网站名称</td>
                <td style="padding: 12px 15px;">' . htmlspecialchars($web_data['web_name']) . '</td>
            </tr>
            <tr style="border-bottom: 1px solid #dee2e6;">
                <td style="padding: 12px 15px; font-weight: bold; background: #f8f9fa;">网站地址</td>
                <td style="padding: 12px 15px;"><a href="http://' . htmlspecialchars($web_data['web_url']) . '" target="_blank" style="color: #007bff; text-decoration: none;">' . htmlspecialchars($web_data['web_url']) . '</a></td>
            </tr>
            <tr style="border-bottom: 1px solid #dee2e6;">
                <td style="padding: 12px 15px; font-weight: bold; background: #f8f9fa;">所属分类</td>
                <td style="padding: 12px 15px;">' . $category_name . '</td>
            </tr>
            <tr style="border-bottom: 1px solid #dee2e6;">
                <td style="padding: 12px 15px; font-weight: bold; background: #f8f9fa;">TAG标签</td>
                <td style="padding: 12px 15px;">' . htmlspecialchars($web_data['web_tags']) . '</td>
            </tr>
            <tr style="border-bottom: 1px solid #dee2e6;">
                <td style="padding: 12px 15px; font-weight: bold; background: #f8f9fa;">网站简介</td>
                <td style="padding: 12px 15px;">' . htmlspecialchars(substr($web_data['web_intro'], 0, 200)) . '</td>
            </tr>
            <tr style="border-bottom: 1px solid #dee2e6;">
                <td style="padding: 12px 15px; font-weight: bold; background: #f8f9fa;">提交用户</td>
                <td style="padding: 12px 15px;">快速提交用户（非会员）</td>
            </tr>
            <tr>
                <td style="padding: 12px 15px; font-weight: bold; background: #f8f9fa;">提交时间</td>
                <td style="padding: 12px 15px;">' . date('Y年m月d日 H:i:s', $web_data['web_ctime']) . '</td>
            </tr>
        </table>

        <div style="text-align: center; margin: 30px 0;">
            <a href="' . $options['site_url'] . 'system/" target="_blank" style="background: linear-gradient(135deg, #28a745, #20c997); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; font-weight: bold; display: inline-block; box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3); transition: all 0.3s ease;">
                🚀 立即前往后台审核
            </a>
        </div>

        <p style="color: #6c757d; font-size: 14px; margin-top: 30px;">
            💡 <strong>温馨提示：</strong>请及时处理网站审核，以提升用户体验。
        </p>
    </div>

    <div style="background: #343a40; color: white; padding: 20px; text-align: center; font-size: 12px; border-radius: 0 0 10px 10px;">
        <p style="margin: 0;">此邮件由 <a href="' . $options['site_url'] . '" target="_blank" style="color: #ffc107; text-decoration: none;">' . htmlspecialchars($options['site_name']) . '</a> 系统自动发送，请勿直接回复。</p>
        <p style="margin: 5px 0 0 0;">© ' . date('Y') . ' ' . htmlspecialchars($options['site_name']) . ' All Rights Reserved.</p>
    </div>
</body>
</html>';

    // 发送邮件
    $result = sendmail($options['admin_email'], $subject, $mailbody);

    // 记录发送结果
    $log_msg = "快速提交邮件通知 - " . date('Y-m-d H:i:s') . " - 网站: " . $web_data['web_name'] . " - 结果: " . ($result ? 'SUCCESS' : 'FAILED') . "\n";
    @error_log($log_msg, 3, ROOT_PATH . "data/email_debug.log");

    return $result;
}

smarty_output($tempfile);
?>
