<?php
// 调试快速提交功能
define('IN_IWEBDIR', true);
require_once('config.php');
require_once('source/init.php');

echo "<h2>🔍 快速提交功能调试</h2>";

// 1. 检查数据库连接
echo "<h3>📊 数据库连接检查</h3>";
if (isset($DB) && is_object($DB)) {
    echo "<p>✅ 数据库对象存在</p>";
    
    // 测试数据库连接
    try {
        $test_query = $DB->query("SELECT 1");
        if ($test_query) {
            echo "<p>✅ 数据库连接正常</p>";
        } else {
            echo "<p>❌ 数据库查询失败</p>";
        }
    } catch (Exception $e) {
        echo "<p>❌ 数据库连接异常: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p>❌ 数据库对象不存在</p>";
}

// 2. 检查数据库表结构
echo "<h3>🗃️ 数据库表结构检查</h3>";
$tables_to_check = array('websites', 'webdata', 'categories');

foreach ($tables_to_check as $table_name) {
    $full_table_name = $DB->table($table_name);
    echo "<h4>表: {$full_table_name}</h4>";
    
    try {
        $query = $DB->query("SHOW TABLES LIKE '{$full_table_name}'");
        if ($DB->num_rows($query)) {
            echo "<p>✅ 表存在</p>";
            
            // 检查表结构
            $structure_query = $DB->query("DESCRIBE {$full_table_name}");
            if ($structure_query) {
                echo "<table border='1' style='border-collapse: collapse;'>";
                echo "<tr><th>字段名</th><th>类型</th><th>是否为空</th><th>键</th><th>默认值</th></tr>";
                
                while ($row = $DB->fetch_array($structure_query)) {
                    echo "<tr>";
                    echo "<td>{$row['Field']}</td>";
                    echo "<td>{$row['Type']}</td>";
                    echo "<td>{$row['Null']}</td>";
                    echo "<td>{$row['Key']}</td>";
                    echo "<td>{$row['Default']}</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
        } else {
            echo "<p>❌ 表不存在</p>";
        }
    } catch (Exception $e) {
        echo "<p>❌ 检查表时出错: " . $e->getMessage() . "</p>";
    }
}

// 3. 检查必要的函数
echo "<h3>🔧 必要函数检查</h3>";
$functions_to_check = array(
    'get_client_ip',
    'update_cache',
    'get_category_option',
    'send_quicksubmit_email_notification'
);

foreach ($functions_to_check as $func_name) {
    if (function_exists($func_name)) {
        echo "<p>✅ {$func_name} 函数存在</p>";
    } else {
        echo "<p>❌ {$func_name} 函数不存在</p>";
    }
}

// 4. 检查配置选项
echo "<h3>⚙️ 配置选项检查</h3>";
$config_keys = array('is_enabled_submit', 'submit_close_reason', 'site_name', 'site_url');

foreach ($config_keys as $key) {
    $value = isset($options[$key]) ? $options[$key] : '未设置';
    echo "<p><strong>{$key}:</strong> {$value}</p>";
}

// 5. 模拟提交测试
echo "<h3>🧪 模拟提交测试</h3>";

// 模拟POST数据
$test_data = array(
    'act' => 'submit',
    'cate_id' => '1',
    'web_name' => '测试网站',
    'web_url' => 'test-' . time() . '.com',
    'web_tags' => '测试,网站',
    'web_intro' => '这是一个测试网站的简介，用于验证快速提交功能是否正常工作。',
    'web_owner' => '测试用户',
    'web_email' => '<EMAIL>',
    'check_code' => 'test'
);

echo "<p><strong>测试数据:</strong></p>";
echo "<pre>";
print_r($test_data);
echo "</pre>";

// 检查分类是否存在
$cate_query = $DB->query("SELECT cate_id, cate_name FROM ".$DB->table('categories')." WHERE cate_id=1");
if ($DB->num_rows($cate_query)) {
    $cate_info = $DB->fetch_array($cate_query);
    echo "<p>✅ 测试分类存在: {$cate_info['cate_name']}</p>";
} else {
    echo "<p>❌ 测试分类不存在，请选择其他分类ID</p>";
    
    // 显示可用分类
    $all_cates = $DB->query("SELECT cate_id, cate_name FROM ".$DB->table('categories')." LIMIT 5");
    if ($DB->num_rows($all_cates)) {
        echo "<p><strong>可用分类:</strong></p>";
        echo "<ul>";
        while ($cate = $DB->fetch_array($all_cates)) {
            echo "<li>ID: {$cate['cate_id']}, 名称: {$cate['cate_name']}</li>";
        }
        echo "</ul>";
    }
}

// 6. 检查错误日志
echo "<h3>📋 错误日志检查</h3>";
$log_files = array(
    ROOT_PATH . "data/email_error.log",
    ROOT_PATH . "data/email_debug.log",
    ROOT_PATH . "data/error.log"
);

foreach ($log_files as $log_file) {
    if (file_exists($log_file)) {
        $log_size = filesize($log_file);
        echo "<p>✅ {$log_file} 存在 (大小: {$log_size} 字节)</p>";
        
        if ($log_size > 0 && $log_size < 10240) { // 小于10KB才显示内容
            $log_content = file_get_contents($log_file);
            $log_lines = explode("\n", $log_content);
            $recent_lines = array_slice($log_lines, -10); // 显示最后10行
            
            echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #ddd; max-height: 200px; overflow-y: auto;'>";
            echo htmlspecialchars(implode("\n", $recent_lines));
            echo "</pre>";
        }
    } else {
        echo "<p>⚠️ {$log_file} 不存在</p>";
    }
}

// 7. 实际测试建议
echo "<h3>🚀 实际测试建议</h3>";
echo "<ol>";
echo "<li>访问快速提交页面: <a href='?mod=quicksubmit' target='_blank'>?mod=quicksubmit</a></li>";
echo "<li>填写表单并提交</li>";
echo "<li>如果出现白屏，检查浏览器开发者工具的控制台和网络选项卡</li>";
echo "<li>检查服务器错误日志</li>";
echo "<li>确认数据是否插入到数据库中</li>";
echo "</ol>";

// 8. 调试模式建议
echo "<h3>🔧 调试模式建议</h3>";
echo "<p>在quicksubmit.php文件开头添加以下代码启用错误显示:</p>";
echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #ddd;'>";
echo "error_reporting(E_ALL);\n";
echo "ini_set('display_errors', 1);\n";
echo "ini_set('log_errors', 1);\n";
echo "ini_set('error_log', ROOT_PATH . 'data/php_error.log');";
echo "</pre>";

echo "<p>然后在关键位置添加调试输出:</p>";
echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #ddd;'>";
echo "// 在数据库插入前\n";
echo "error_log('准备插入数据: ' . print_r(\$web_data, true), 3, ROOT_PATH . 'data/debug.log');\n\n";
echo "// 在数据库插入后\n";
echo "error_log('插入结果 - ID: ' . \$web_id, 3, ROOT_PATH . 'data/debug.log');";
echo "</pre>";
?>
